import { createSlice, PayloadAction, createSelector } from '@reduxjs/toolkit';
import { RootState } from '../store'; // Assuming RootState is defined here

export interface TimerState {
  isRunning: boolean;
  elapsedTime: number; // Accumulated time from previous (paused) sessions, in seconds
  startTime: number | null; // Timestamp (ms) when timer started or resumed for current session
  dailyTotal: number; // total time worked today in seconds (accumulated from saves/endDay)
  lastSaveDate: string | null; // date when last saved to history (e.g., end of day)
}

const initialState: TimerState = {
  isRunning: false,
  elapsedTime: 0,
  startTime: null,
  dailyTotal: 0,
  lastSaveDate: null,
};

const timerSlice = createSlice({
  name: 'timer',
  initialState,
  reducers: {
    startTimer: (state) => {
      if (!state.isRunning) {
        state.isRunning = true;
        state.startTime = Date.now(); // Record current timestamp as start time
      }
    },

    pauseTimer: (state) => {
      if (state.isRunning && state.startTime !== null) {
        state.isRunning = false;
        const currentTime = Date.now();
        const sessionDuration = Math.floor(
          (currentTime - state.startTime) / 1000
        );
        state.elapsedTime += sessionDuration; // Add current session's time to accumulated total
        state.startTime = null; // Clear start time as session is paused
      }
    },

    resetTimer: (state) => {
      state.isRunning = false;
      state.elapsedTime = 0; // Reset accumulated session time
      state.startTime = null; // Clear start time
    },

    saveDailyTotal: (state) => {
      const today = new Date().toISOString().split('T')[0];
      // Calculate the *total* time currently elapsed (including any running session)
      let totalCurrentElapsed = state.elapsedTime;
      if (state.isRunning && state.startTime !== null) {
        totalCurrentElapsed += Math.floor(
          (Date.now() - state.startTime) / 1000
        );
      }

      state.dailyTotal += totalCurrentElapsed; // Add this total to the daily sum
      state.lastSaveDate = today;

      // Reset the current timer session for a new task/segment
      state.elapsedTime = 0;
      state.startTime = state.isRunning ? Date.now() : null; // If still running, restart startTime from now
      // to continue tracking a new session for the same task type
    },

    endDay: (state) => {
      const today = new Date().toISOString().split('T')[0];
      // Calculate the *total* time currently elapsed (including any running session)
      let finalDaySessionTotal = state.elapsedTime;
      if (state.isRunning && state.startTime !== null) {
        finalDaySessionTotal += Math.floor(
          (Date.now() - state.startTime) / 1000
        );
      }

      state.dailyTotal += finalDaySessionTotal; // Add final session total to daily sum
      state.lastSaveDate = today;

      // Reset all timer values for the next day
      state.isRunning = false;
      state.elapsedTime = 0;
      state.startTime = null;
      state.dailyTotal = 0; // Reset daily total for the new day
    },

    loadDailyState: (
      state,
      action: PayloadAction<{ date: string; totalTime: number }>
    ) => {
      const today = new Date().toISOString().split('T')[0];
      if (action.payload.date === today) {
        state.dailyTotal = action.payload.totalTime;
        state.lastSaveDate = action.payload.date;
      }
    },
  },
});

export const {
  startTimer,
  pauseTimer,
  resetTimer,
  saveDailyTotal, // Removed updateElapsedTime from exports
  endDay,
  loadDailyState,
} = timerSlice.actions;

// Selectors
export const selectTimerState = (state: RootState) => state.timer; // Use RootState directly

export const selectIsTimerRunning = createSelector(
  selectTimerState,
  (timer) => timer.isRunning
);

export const selectCurrentElapsedTime = createSelector(
  selectTimerState,
  (timer) => {
    if (timer.isRunning && timer.startTime !== null) {
      const currentTime = Date.now();
      const sessionDuration = Math.floor(
        (currentTime - timer.startTime) / 1000
      );
      return timer.elapsedTime + sessionDuration; // Accumulated paused time + current session time
    }
    return timer.elapsedTime; // If paused, just return the accumulated time
  }
);

export const selectDailyTotal = createSelector(
  selectTimerState,
  (timer) => timer.dailyTotal
);

export default timerSlice.reducer;
