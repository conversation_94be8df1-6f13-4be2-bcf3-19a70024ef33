import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Button } from './Button';
import { Card } from './Card';
import { useAppDispatch, useAppSelector } from '@/store/store';
import {
  startTimer,
  pauseTimer,
  selectIsTimerRunning,
  selectCurrentElapsedTime,
} from '@/store/slices/timerSlice';
import { Play, Pause } from 'lucide-react-native';

interface TimerProps {
  onSaveToHistory?: (totalTime: number) => void;
}

export const Timer: React.FC<TimerProps> = ({ onSaveToHistory }) => {
  const dispatch = useAppDispatch();
  const isRunning = useAppSelector(selectIsTimerRunning);
  // This selector re-evaluates on *each component re-render*,
  // and dynamically calculates time using Date.now() if running.
  const elapsedTime = useAppSelector(selectCurrentElapsedTime);

  // Use a local state to force component re-renders every second when the timer is running.
  // This state itself doesn't hold the time; it just acts as a trigger for re-render.
  const [, setForceUpdate] = useState(0);

  // Set up the interval to force re-renders for the timer display
  useEffect(() => {
    let intervalId: number | null = null; // Use 'number' for setInterval ID in React Native/browser

    if (isRunning) {
      intervalId = setInterval(() => {
        // Incrementing a dummy state forces a re-render.
        // On re-render, `useAppSelector(selectCurrentElapsedTime)` is re-evaluated,
        // which then recalculates the elapsed time based on Date.now().
        setForceUpdate((prev) => prev + 1);
      }, 1000); // Update every second
    }

    // Cleanup function: Clear the interval when the component unmounts
    // or when `isRunning` changes (e.g., timer is paused/stopped).
    return () => {
      if (intervalId !== null) {
        clearInterval(intervalId);
      }
    };
  }, [isRunning]); // Re-run this effect only when the `isRunning` status changes

  // Format time from seconds to HH:MM:SS
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    return `${hours.toString().padStart(2, '0')}:${minutes
      .toString()
      .padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleToggle = () => {
    if (isRunning) {
      dispatch(pauseTimer());
    } else {
      dispatch(startTimer());
    }
  };

  return (
    <Card style={styles.container}>
      <View style={styles.timerDisplay}>
        <Text style={styles.timerText}>{formatTime(elapsedTime)}</Text>
        <Text style={styles.timerLabel}>Temps de travail</Text>
      </View>

      <View style={styles.buttonContainer}>
        <Button
          title={isRunning ? 'Arrêter' : 'Démarrer'}
          variant={isRunning ? 'danger' : 'primary'}
          size="large"
          onPress={handleToggle}
          style={styles.toggleButton}
          icon={
            isRunning ? (
              <Pause size={20} color="#ffffff" />
            ) : (
              <Play size={20} color="#ffffff" />
            )
          }
        />
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    alignItems: 'center',
  },
  timerDisplay: {
    alignItems: 'center',
    marginBottom: 32,
  },
  timerText: {
    fontSize: 56,
    fontWeight: '700',
    color: '#111827',
    fontFamily: 'monospace',
    letterSpacing: 2,
  },
  timerLabel: {
    fontSize: 18,
    color: '#6b7280',
    marginTop: 12,
    fontWeight: '500',
  },
  buttonContainer: {
    width: '100%',
    alignItems: 'center',
  },
  toggleButton: {
    minWidth: 200,
    paddingHorizontal: 32,
  },
});
