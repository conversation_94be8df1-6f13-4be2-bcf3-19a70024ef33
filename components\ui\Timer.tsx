import React, { useEffect, useState } from 'react'; // Import useState
import { View, Text, StyleSheet } from 'react-native';
import { Button } from './Button'; // Assuming Button component path
import { Card } from './Card'; // Assuming Card component path
import { useAppDispatch, useAppSelector } from '@/store/store'; // Adjust path if needed
import {
  startTimer,
  pauseTimer,
  resetTimer,
  selectIsTimerRunning,
  selectCurrentElapsedTime, // This selector now correctly calculates dynamic time
  selectDailyTotal,
  saveDailyTotal,
  endDay,
} from '@/store/slices/timerSlice'; // Ensure this path is correct
import { Play, Pause, Square, Save } from 'lucide-react-native';

interface TimerProps {
  onSaveToHistory?: (totalTime: number) => void;
}

export const Timer: React.FC<TimerProps> = ({ onSaveToHistory }) => {
  const dispatch = useAppDispatch();
  const isRunning = useAppSelector(selectIsTimerRunning);
  // This selector re-evaluates on *each component re-render*,
  // and dynamically calculates time using Date.now() if running.
  const elapsedTime = useAppSelector(selectCurrentElapsedTime);
  const dailyTotal = useAppSelector(selectDailyTotal);

  // Use a local state to force component re-renders every second when the timer is running.
  // This state itself doesn't hold the time; it just acts as a trigger for re-render.
  const [, setForceUpdate] = useState(0);

  // Set up the interval to force re-renders for the timer display
  useEffect(() => {
    let intervalId: number | null = null; // Use 'number' for setInterval ID in React Native/browser

    if (isRunning) {
      intervalId = setInterval(() => {
        // Incrementing a dummy state forces a re-render.
        // On re-render, `useAppSelector(selectCurrentElapsedTime)` is re-evaluated,
        // which then recalculates the elapsed time based on Date.now().
        setForceUpdate((prev) => prev + 1);
      }, 1000); // Update every second
    }

    // Cleanup function: Clear the interval when the component unmounts
    // or when `isRunning` changes (e.g., timer is paused/stopped).
    return () => {
      if (intervalId !== null) {
        clearInterval(intervalId);
      }
    };
  }, [isRunning]); // Re-run this effect only when the `isRunning` status changes

  // Format time from seconds to HH:MM:SS
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    return `${hours.toString().padStart(2, '0')}:${minutes
      .toString()
      .padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStart = () => {
    dispatch(startTimer());
  };

  const handlePause = () => {
    dispatch(pauseTimer());
  };

  const handleReset = () => {
    dispatch(resetTimer());
  };

  const handleSave = () => {
    dispatch(saveDailyTotal());
    if (onSaveToHistory) {
      onSaveToHistory(elapsedTime); // Pass the currently displayed elapsed time
    }
  };

  const handleEndDay = () => {
    dispatch(endDay());
    if (onSaveToHistory) {
      // You might want to pass the final dailyTotal here,
      // but `endDay` might reset it to 0 immediately.
      // If history needs the *final* daily total *before* reset,
      // consider dispatching an action that captures and saves it first,
      // then resets. For now, passing current elapsed time.
      onSaveToHistory(elapsedTime);
    }
  };

  return (
    <Card style={styles.container}>
      <View style={styles.timerDisplay}>
        <Text style={styles.timerText}>{formatTime(elapsedTime)}</Text>
        <Text style={styles.timerLabel}>Temps de travail</Text>
      </View>

      <View style={styles.buttonContainer}>
        <View style={styles.buttonRow}>
          {!isRunning ? (
            <Button
              title="Démarrer"
              variant="primary"
              size="medium"
              onPress={handleStart}
              style={styles.button}
              icon={<Play size={16} color="#ffffff" />}
              // Disable if timer already has some time and is just paused (can resume)
              // Or if you only want to start from 0 after a reset.
              // For a simple start/pause, always enabled unless a specific state prevents it.
              disabled={false}
            />
          ) : (
            <Button
              title="Pause"
              variant="secondary"
              size="medium"
              onPress={handlePause}
              style={styles.button}
              icon={<Pause size={16} color="#6b7280" />}
              disabled={false}
            />
          )}

          <Button
            title="Reset"
            variant="outline"
            size="medium"
            onPress={handleReset}
            style={styles.button}
            icon={<Square size={16} color="#6b7280" />}
            disabled={elapsedTime === 0 && !isRunning} // Disable if no time has passed and not running
          />
        </View>

        <View style={styles.buttonRow}>
          <Button
            title="Sauvegarder"
            variant="success"
            size="medium"
            onPress={handleSave}
            style={styles.button}
            icon={<Save size={16} color="#ffffff" />}
            disabled={elapsedTime === 0 && !isRunning} // Disable if no time has passed to save
          />

          <Button
            title="Fin de journée"
            variant="danger"
            size="medium"
            onPress={handleEndDay}
            style={styles.button}
            // Disable if no daily total and no current session time
            disabled={dailyTotal === 0 && elapsedTime === 0 && !isRunning}
          />
        </View>
      </View>

      {dailyTotal > 0 && ( // Only show if there's a daily total
        <View style={styles.dailyTotalContainer}>
          <Text style={styles.dailyTotalLabel}>Total journée:</Text>
          <Text style={styles.dailyTotalText}>{formatTime(dailyTotal)}</Text>
        </View>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    alignItems: 'center',
  },
  timerDisplay: {
    alignItems: 'center',
    marginBottom: 24,
  },
  timerText: {
    fontSize: 48,
    fontWeight: '700',
    color: '#111827',
    fontFamily: 'monospace',
    letterSpacing: 2,
  },
  timerLabel: {
    fontSize: 16,
    color: '#6b7280',
    marginTop: 8,
    fontWeight: '500',
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  button: {
    flex: 1,
  },
  dailyTotalContainer: {
    marginTop: 20,
    padding: 12,
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    alignItems: 'center',
    width: '100%',
  },
  dailyTotalLabel: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
  },
  dailyTotalText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginTop: 4,
    fontFamily: 'monospace',
  },
});
