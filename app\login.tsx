import { View, Text, StyleSheet, Alert } from 'react-native';
import { useState } from 'react';
import { ScreenWrapper } from '@/components/ui/ScreenWrapper';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { InputField } from '@/components/ui/InputField';
import { useLoginMutation } from '@/store/api/apiSlice';
import { AuthService } from '@/services/authService';
import { AuthDebug } from '@/components/debug/AuthDebug';

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [login, { isLoading }] = useLoginMutation();

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs');
      return;
    }

    console.log('[Login] Attempting login with:', { email, password: '***' });

    try {
      // Use RTK Query mutation
      console.log('[Login] Calling login mutation...');
      const result = await login({ email, password }).unwrap();
      console.log('[Login] Login mutation successful:', result);

      // Save auth data using AuthService
      console.log('[Login] Saving auth data...');
      await AuthService.saveAuthData(
        result.user,
        result.token,
        result.refreshToken
      );
      console.log(
        '[Login] Auth data saved, navigation should happen automatically'
      );

      // Navigation will be handled by AuthNavigator
    } catch (error: any) {
      console.error('[Login] Login error:', error);
      Alert.alert(
        'Erreur',
        error?.data?.message || 'Email ou mot de passe incorrect'
      );
    }
  };

  return (
    <ScreenWrapper>
      {/* <AuthDebug /> */}
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Connexion</Text>
          <Text style={styles.subtitle}>Accédez à votre espace de travail</Text>
        </View>

        <Card style={styles.card}>
          <InputField
            label="Email"
            value={email}
            onChangeText={setEmail}
            placeholder="<EMAIL>"
            keyboardType="email-address"
          />

          <InputField
            label="Mot de passe"
            value={password}
            onChangeText={setPassword}
            placeholder="Votre mot de passe"
            secureTextEntry={!showPassword}
          />

          <Button
            title="Se connecter"
            onPress={handleLogin}
            loading={isLoading}
            style={styles.loginButton}
          />

          <Button
            title="Mot de passe oublié ?"
            variant="secondary"
            onPress={() => {}}
            style={styles.forgotButton}
          />
        </Card>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Première connexion ? Contactez votre administrateur
          </Text>
        </View>
      </View>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
    backgroundColor: '#f9fafb',
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 32,
    fontWeight: '700',
    color: '#111827',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  card: {
    marginBottom: 24,
  },
  loginButton: {
    marginTop: 8,
  },
  forgotButton: {
    marginTop: 12,
  },
  footer: {
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#9ca3af',
    textAlign: 'center',
  },
});
